import test from 'ava';

// Helper functions for navigation testing
function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month, 0).getDate();
}

function findLastDayOfWeekInMonth(targetYear: number, targetMonth: number, dayOfWeek: number): number {
  const daysInTargetMonth = getDaysInMonth(targetYear, targetMonth);

  for (let day = daysInTargetMonth; day >= 1; day--) {
    const date = new Date(targetYear, targetMonth - 1, day);
    if (date.getDay() === dayOfWeek) {
      return day;
    }
  }

  return daysInTargetMonth;
}

function findFirstDayOfWeekInMonth(targetYear: number, targetMonth: number, dayOfWeek: number): number {
  const daysInTargetMonth = getDaysInMonth(targetYear, targetMonth);

  for (let day = 1; day <= daysInTargetMonth; day++) {
    const date = new Date(targetYear, targetMonth - 1, day);
    if (date.getDay() === dayOfWeek) {
      return day;
    }
  }

  return 1;
}

function getCurrentDayOfWeek(selectedDay: number, year: number, month: number): number {
  const date = new Date(year, month - 1, selectedDay);
  return date.getDay();
}

// Simulate left navigation logic
function simulateNavigateLeft(currentYear: number, currentMonth: number, currentDay: number) {
  // Check if we're on the first day of the month
  if (currentDay === 1) {
    let newMonth = currentMonth - 1;
    let newYear = currentYear;

    if (newMonth < 1) {
      newMonth = 12;
      newYear = currentYear - 1;
    }

    const currentDayOfWeek = getCurrentDayOfWeek(currentDay, currentYear, currentMonth);
    const targetDay = findLastDayOfWeekInMonth(newYear, newMonth, currentDayOfWeek);

    return { year: newYear, month: newMonth, day: targetDay };
  }

  // Otherwise, just move to previous day
  return { year: currentYear, month: currentMonth, day: currentDay - 1 };
}

// Simulate right navigation logic
function simulateNavigateRight(currentYear: number, currentMonth: number, currentDay: number) {
  const daysInCurrentMonth = getDaysInMonth(currentYear, currentMonth);

  // Check if we're on the last day of the month
  if (currentDay === daysInCurrentMonth) {
    let newMonth = currentMonth + 1;
    let newYear = currentYear;

    if (newMonth > 12) {
      newMonth = 1;
      newYear = currentYear + 1;
    }

    const currentDayOfWeek = getCurrentDayOfWeek(currentDay, currentYear, currentMonth);
    const targetDay = findFirstDayOfWeekInMonth(newYear, newMonth, currentDayOfWeek);

    return { year: newYear, month: newMonth, day: targetDay };
  }

  // Otherwise, just move to next day
  return { year: currentYear, month: currentMonth, day: currentDay + 1 };
}

// Test left navigation from first day of month
test('LEFT from July 1st goes to last Tuesday of June', t => {
  const result = simulateNavigateLeft(2025, 7, 1);

  t.is(result.year, 2025);
  t.is(result.month, 6);
  t.is(result.day, 24); // June 24, 2025 is the last Tuesday

  // Verify both days are Tuesdays
  const originalDayOfWeek = getCurrentDayOfWeek(1, 2025, 7);
  const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
  t.is(originalDayOfWeek, 2); // Tuesday
  t.is(targetDayOfWeek, 2); // Tuesday
});

test('LEFT from January 1st goes to last Wednesday of December previous year', t => {
  const result = simulateNavigateLeft(2025, 1, 1);

  t.is(result.year, 2024);
  t.is(result.month, 12);
  t.is(result.day, 25); // December 25, 2024 is the last Wednesday

  // Verify both days are Wednesdays
  const originalDayOfWeek = getCurrentDayOfWeek(1, 2025, 1);
  const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
  t.is(originalDayOfWeek, 3); // Wednesday
  t.is(targetDayOfWeek, 3); // Wednesday
});

test('LEFT from February 1st goes to last Saturday of January', t => {
  const result = simulateNavigateLeft(2025, 2, 1);

  t.is(result.year, 2025);
  t.is(result.month, 1);
  t.is(result.day, 25); // January 25, 2025 is the last Saturday

  // Verify both days are Saturdays
  const originalDayOfWeek = getCurrentDayOfWeek(1, 2025, 2);
  const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
  t.is(originalDayOfWeek, 6); // Saturday
  t.is(targetDayOfWeek, 6); // Saturday
});

// Test right navigation from last day of month
test('RIGHT from July 31st goes to first Thursday of August', t => {
  const result = simulateNavigateRight(2025, 7, 31);

  t.is(result.year, 2025);
  t.is(result.month, 8);
  t.is(result.day, 7); // August 7, 2025 is the first Thursday

  // Verify both days are Thursdays
  const originalDayOfWeek = getCurrentDayOfWeek(31, 2025, 7);
  const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
  t.is(originalDayOfWeek, 4); // Thursday
  t.is(targetDayOfWeek, 4); // Thursday
});

test('RIGHT from December 31st goes to first Tuesday of January next year', t => {
  const result = simulateNavigateRight(2024, 12, 31);

  t.is(result.year, 2025);
  t.is(result.month, 1);
  t.is(result.day, 7); // January 7, 2025 is the first Tuesday

  // Verify both days are Tuesdays
  const originalDayOfWeek = getCurrentDayOfWeek(31, 2024, 12);
  const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
  t.is(originalDayOfWeek, 2); // Tuesday
  t.is(targetDayOfWeek, 2); // Tuesday
});

test('RIGHT from February 28th goes to first Friday of March', t => {
  const result = simulateNavigateRight(2025, 2, 28);

  t.is(result.year, 2025);
  t.is(result.month, 3);
  t.is(result.day, 7); // March 7, 2025 is the first Friday

  // Verify both days are Fridays
  const originalDayOfWeek = getCurrentDayOfWeek(28, 2025, 2);
  const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
  t.is(originalDayOfWeek, 5); // Friday
  t.is(targetDayOfWeek, 5); // Friday
});

test('RIGHT from February 29th (leap year) goes to first Thursday of March', t => {
  const result = simulateNavigateRight(2024, 2, 29);

  t.is(result.year, 2024);
  t.is(result.month, 3);
  t.is(result.day, 7); // March 7, 2024 is the first Thursday

  // Verify both days are Thursdays
  const originalDayOfWeek = getCurrentDayOfWeek(29, 2024, 2);
  const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
  t.is(originalDayOfWeek, 4); // Thursday
  t.is(targetDayOfWeek, 4); // Thursday
});

// Test normal within-month navigation
test('LEFT from July 15th goes to July 14th (normal navigation)', t => {
  const result = simulateNavigateLeft(2025, 7, 15);

  t.is(result.year, 2025);
  t.is(result.month, 7);
  t.is(result.day, 14);
});

test('RIGHT from July 15th goes to July 16th (normal navigation)', t => {
  const result = simulateNavigateRight(2025, 7, 15);

  t.is(result.year, 2025);
  t.is(result.month, 7);
  t.is(result.day, 16);
});

// Test edge cases with different month lengths
test('LEFT from March 1st goes to last Saturday of February (non-leap year)', t => {
  const result = simulateNavigateLeft(2025, 3, 1);

  t.is(result.year, 2025);
  t.is(result.month, 2);
  t.is(result.day, 22); // February 22, 2025 is the last Saturday

  // Verify both days are Saturdays
  const originalDayOfWeek = getCurrentDayOfWeek(1, 2025, 3);
  const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
  t.is(originalDayOfWeek, 6); // Saturday
  t.is(targetDayOfWeek, 6); // Saturday
});

test('RIGHT from April 30th goes to first Wednesday of May', t => {
  const result = simulateNavigateRight(2025, 4, 30);

  t.is(result.year, 2025);
  t.is(result.month, 5);
  t.is(result.day, 7); // May 7, 2025 is the first Wednesday

  // Verify both days are Wednesdays
  const originalDayOfWeek = getCurrentDayOfWeek(30, 2025, 4);
  const targetDayOfWeek = getCurrentDayOfWeek(result.day, result.year, result.month);
  t.is(originalDayOfWeek, 3); // Wednesday
  t.is(targetDayOfWeek, 3); // Wednesday
});
