import { Box, Text, useInput } from 'ink';
import { useCallback, useEffect, useState } from 'react';

type DayData = {
  readonly entered: number;
  readonly expected: number;
};

type CalendarProps = {
  readonly month: number; // 1-12
  readonly year: number;
  readonly dayData?: Record<number, DayData>;
  readonly onMonthChange?: (month: number, year: number) => void;
};

type DayCellProps = {
  readonly day: number | undefined;
  readonly dayIndex: number;
  readonly month: number;
  readonly year: number;
  readonly dayData?: Record<number, DayData> | undefined;
  readonly isSelected: boolean;
};

const daysInWeek = 7;
const dayNames = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];
const monthNames = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

function getDaysInMonth(year: number, month: number): number {
  return new Date(year, month, 0).getDate();
}

function getFirstDayOfMonth(year: number, month: number): number {
  return new Date(year, month - 1, 1).getDay();
}

function isToday(
  day: number | undefined,
  month: number,
  year: number
): boolean {
  if (day === undefined) {
    return false;
  }

  const today = new Date();
  return (
    today.getDate() === day &&
    today.getMonth() + 1 === month &&
    today.getFullYear() === year
  );
}

function getDiffColor(diff: number): 'green' | 'red' | 'white' {
  if (diff > 0) {
    return 'green';
  }
  if (diff < 0) {
    return 'red';
  }
  return 'white';
}

function getBorderColor(isSelected: boolean, isTodayFlag: boolean): string {
  if (isSelected) {
    return 'cyan';
  }
  if (isTodayFlag) {
    return 'yellow';
  }
  return 'gray';
}

function DayCell({
  day,
  dayIndex,
  month,
  year,
  dayData,
  isSelected,
}: DayCellProps) {
  const isCurrentMonth = day !== undefined;
  const isTodayFlag = isToday(day, month, year);

  const info = isCurrentMonth && dayData ? dayData[day] : undefined;
  const entered = info ? info.entered : 0;
  const expected = info ? info.expected : 0;
  const diff = entered - expected;

  const diffColor = getDiffColor(diff);
  const borderColor = getBorderColor(isSelected, isTodayFlag);

  return (
    <Box
      borderColor={borderColor}
      borderStyle="single"
      flexDirection="column"
      gap={0}
      height={7}
      key={String(day ?? `blank-${dayIndex}`)}
      margin={0}
      padding={0}
      width="14.28%"
    >
      <Box justifyContent="flex-end" width="100%">
        <Text
          bold={isTodayFlag || isSelected}
          color={isCurrentMonth ? 'white' : 'gray'}
        >
          {day ?? ''}
        </Text>
      </Box>
      {isCurrentMonth ? (
        <Box alignItems="center" flexDirection="column">
          <Text>
            {entered}h/{expected}h
          </Text>
          <Text color={diffColor}>
            {diff >= 0 ? '+' : ''}
            {diff}h
          </Text>
        </Box>
      ) : null}
    </Box>
  );
}

function generateCalendarDays(
  year: number,
  month: number
): Array<number | undefined> {
  const daysInMonth = getDaysInMonth(year, month);
  const firstDay = getFirstDayOfMonth(year, month);
  const days: Array<number | undefined> = [];

  // Add empty slots for days before the 1st of the month
  for (let i = 0; i < firstDay; i++) {
    days.push(undefined);
  }

  // Add all days of the month
  for (let day = 1; day <= daysInMonth; day++) {
    days.push(day);
  }

  return days;
}

function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }

  return chunks;
}

export default function Calendar({
  month,
  year,
  dayData,
  onMonthChange,
}: CalendarProps) {
  const days = generateCalendarDays(year, month);
  const weeks = chunkArray(days, daysInWeek);
  const monthName = monthNames[month - 1];

  // Ensure we have 6 weeks to maintain consistent height
  while (weeks.length < 5) {
    weeks.push(new Array(daysInWeek).fill(undefined));
  }

  // State for keyboard navigation
  const [selectedDay, setSelectedDay] = useState<number | null>(null);

  // Helper function to get the first valid day in the month
  const getFirstValidDay = useCallback(() => {
    return days.find((day) => day !== undefined) || 1;
  }, [days]);

  // Initialize selected day to today if it's in the current month
  useEffect(() => {
    const today = new Date();
    if (
      today.getMonth() + 1 === month &&
      today.getFullYear() === year &&
      selectedDay === null
    ) {
      setSelectedDay(today.getDate());
    } else if (selectedDay === null) {
      // If not current month, select the first day of the month
      setSelectedDay(getFirstValidDay());
    }
  }, [month, year, selectedDay, getFirstValidDay]);

  // Reset selected day when month changes (but preserve if possible)
  useEffect(() => {
    if (selectedDay !== null) {
      const daysInCurrentMonth = getDaysInMonth(year, month);
      if (selectedDay > daysInCurrentMonth) {
        // If selected day doesn't exist in new month, select the last day
        setSelectedDay(daysInCurrentMonth);
      }
      // Otherwise keep the same day number if it exists in the new month
    }
  }, [month, year, selectedDay]);

  // Helper function to find day position in the grid
  const findDayPosition = (targetDay: number) => {
    for (let weekIndex = 0; weekIndex < weeks.length; weekIndex++) {
      const week = weeks[weekIndex];
      if (week) {
        const dayIndex = week.indexOf(targetDay);
        if (dayIndex !== -1) {
          return { weekIndex, dayIndex };
        }
      }
    }
    return null;
  };

  // Helper functions for finding valid days
  const findLastValidDayInWeek = (
    week: (number | undefined)[]
  ): number | undefined => {
    for (let i = daysInWeek - 1; i >= 0; i--) {
      if (week[i] !== undefined) {
        return week[i];
      }
    }
    return;
  };

  const findFirstValidDayInWeek = (
    week: (number | undefined)[]
  ): number | undefined => {
    for (let i = 0; i < daysInWeek; i++) {
      if (week[i] !== undefined) {
        return week[i];
      }
    }
    return;
  };

  // Helper function to find the last occurrence of a day of week in a month
  const findLastDayOfWeekInMonth = useCallback(
    (targetYear: number, targetMonth: number, dayOfWeek: number): number => {
      const daysInTargetMonth = getDaysInMonth(targetYear, targetMonth);

      // Start from the last day and work backwards
      for (let day = daysInTargetMonth; day >= 1; day--) {
        const date = new Date(targetYear, targetMonth - 1, day);
        if (date.getDay() === dayOfWeek) {
          return day;
        }
      }

      // Fallback to last day if no match found (shouldn't happen)
      return daysInTargetMonth;
    },
    []
  );

  // Helper function to find the first occurrence of a day of week in a month
  const findFirstDayOfWeekInMonth = useCallback(
    (targetYear: number, targetMonth: number, dayOfWeek: number): number => {
      const daysInTargetMonth = getDaysInMonth(targetYear, targetMonth);

      // Start from the first day and work forwards
      for (let day = 1; day <= daysInTargetMonth; day++) {
        const date = new Date(targetYear, targetMonth - 1, day);
        if (date.getDay() === dayOfWeek) {
          return day;
        }
      }

      // Fallback to first day if no match found (shouldn't happen)
      return 1;
    },
    []
  );

  // Month navigation helper functions
  const navigateToPreviousMonth = useCallback(
    (targetDayOfWeek?: number) => {
      if (!onMonthChange) {
        return;
      }

      let newMonth = month - 1;
      let newYear = year;

      if (newMonth < 1) {
        newMonth = 12;
        newYear = year - 1;
      }

      onMonthChange(newMonth, newYear);

      // If a target day of week is specified, select the last occurrence of that day in the previous month
      if (targetDayOfWeek !== undefined) {
        const targetDay = findLastDayOfWeekInMonth(
          newYear,
          newMonth,
          targetDayOfWeek
        );
        setSelectedDay(targetDay);
      }
    },
    [month, year, onMonthChange, findLastDayOfWeekInMonth]
  );

  const navigateToNextMonth = useCallback(
    (targetDayOfWeek?: number) => {
      if (!onMonthChange) {
        return;
      }

      let newMonth = month + 1;
      let newYear = year;

      if (newMonth > 12) {
        newMonth = 1;
        newYear = year + 1;
      }

      onMonthChange(newMonth, newYear);

      // If a target day of week is specified, select the first occurrence of that day in the next month
      if (targetDayOfWeek !== undefined) {
        const targetDay = findFirstDayOfWeekInMonth(
          newYear,
          newMonth,
          targetDayOfWeek
        );
        setSelectedDay(targetDay);
      }
    },
    [month, year, onMonthChange, findFirstDayOfWeekInMonth]
  );

  // Helper function to get the day of week for the currently selected day
  const getCurrentDayOfWeek = useCallback((): number => {
    if (!selectedDay) {
      return 0; // Sunday as fallback
    }
    const date = new Date(year, month - 1, selectedDay);
    return date.getDay();
  }, [selectedDay, year, month]);

  // Navigation helper functions
  const navigateLeft = (weekIndex: number, dayIndex: number) => {
    if (dayIndex > 0) {
      // Move to previous day in same week
      const currentWeek = weeks[weekIndex];
      const prevDay = currentWeek?.[dayIndex - 1];
      if (prevDay !== undefined) {
        setSelectedDay(prevDay);
      }
      return;
    }

    if (weekIndex > 0) {
      // Move to last day of previous week
      const prevWeek = weeks[weekIndex - 1];
      if (prevWeek) {
        const lastValidDay = findLastValidDayInWeek(prevWeek);
        if (lastValidDay !== undefined) {
          setSelectedDay(lastValidDay);
        }
      }
      return;
    }

    // At the beginning of the month, navigate to previous month
    const currentDayOfWeek = getCurrentDayOfWeek();
    navigateToPreviousMonth(currentDayOfWeek);
  };

  const navigateRight = (weekIndex: number, dayIndex: number) => {
    if (dayIndex < daysInWeek - 1) {
      // Move to next day in same week
      const currentWeek = weeks[weekIndex];
      const nextDay = currentWeek?.[dayIndex + 1];
      if (nextDay !== undefined) {
        setSelectedDay(nextDay);
      }
      return;
    }

    if (weekIndex < weeks.length - 1) {
      // Move to first day of next week
      const nextWeek = weeks[weekIndex + 1];
      if (nextWeek) {
        const firstValidDay = findFirstValidDayInWeek(nextWeek);
        if (firstValidDay !== undefined) {
          setSelectedDay(firstValidDay);
        }
      }
      return;
    }

    // At the end of the month, navigate to next month
    const currentDayOfWeek = getCurrentDayOfWeek();
    navigateToNextMonth(currentDayOfWeek);
  };

  const navigateUp = (weekIndex: number, dayIndex: number) => {
    if (weekIndex <= 0) {
      // At the top of the month, navigate to previous month
      const currentDayOfWeek = getCurrentDayOfWeek();
      navigateToPreviousMonth(currentDayOfWeek);
      return;
    }

    const prevWeek = weeks[weekIndex - 1];
    if (!prevWeek) {
      return;
    }

    const upDay = prevWeek[dayIndex];
    if (upDay !== undefined) {
      setSelectedDay(upDay);
      return;
    }

    // If the cell directly above is empty, navigate to previous month
    const currentDayOfWeek = getCurrentDayOfWeek();
    navigateToPreviousMonth(currentDayOfWeek);
  };

  const navigateDown = (weekIndex: number, dayIndex: number) => {
    if (weekIndex >= weeks.length - 1) {
      // At the bottom of the month, navigate to next month
      const currentDayOfWeek = getCurrentDayOfWeek();
      navigateToNextMonth(currentDayOfWeek);
      return;
    }

    const nextWeek = weeks[weekIndex + 1];
    if (!nextWeek) {
      return;
    }

    const downDay = nextWeek[dayIndex];
    if (downDay !== undefined) {
      setSelectedDay(downDay);
      return;
    }

    // If the cell directly below is empty, navigate to next month
    const currentDayOfWeek = getCurrentDayOfWeek();
    navigateToNextMonth(currentDayOfWeek);
  };

  // Keyboard navigation
  useInput((_input, key) => {
    // Handle Page Up/Page Down for month navigation
    if (key.pageUp || key.pageDown) {
      if (key.pageUp) {
        navigateToPreviousMonth();
      } else {
        navigateToNextMonth();
      }
      return;
    }

    // Handle arrow key navigation
    if (!selectedDay) {
      setSelectedDay(getFirstValidDay());
      return;
    }

    const currentPosition = findDayPosition(selectedDay);
    if (!currentPosition) {
      return;
    }

    const { weekIndex, dayIndex } = currentPosition;

    // Handle all arrow keys
    if (key.leftArrow) {
      navigateLeft(weekIndex, dayIndex);
    } else if (key.rightArrow) {
      navigateRight(weekIndex, dayIndex);
    } else if (key.upArrow) {
      navigateUp(weekIndex, dayIndex);
    } else if (key.downArrow) {
      navigateDown(weekIndex, dayIndex);
    }
  });

  return (
    <Box flexDirection="column" gap={0} margin={0} padding={0} width="100%">
      {/* Month and Year Header */}
      <Box
        borderColor="red"
        borderStyle="single"
        justifyContent="center"
        marginBottom={1}
      >
        <Text bold color="cyan">
          {monthName} {year}
        </Text>
      </Box>

      {/* Calendar Grid Container */}
      <Box
        borderColor="gray"
        borderStyle="single"
        flexDirection="column"
        width="100%"
      >
        {/* Day Names Header */}
        <Box flexDirection="row" gap={0} margin={0} padding={0} width="100%">
          {dayNames.map((day) => (
            <Box
              alignItems="center"
              borderColor="gray"
              borderStyle="single"
              gap={0}
              height={3}
              justifyContent="center"
              key={day}
              margin={0}
              padding={0}
              width="14.28%"
            >
              <Text bold>{day}</Text>
            </Box>
          ))}
        </Box>

        {/* Calendar Rows */}
        {weeks.map((week) => {
          const weekKey =
            week.find((day) => day !== undefined) ?? 'week-placeholder';
          return (
            <Box
              flexDirection="row"
              gap={0}
              key={String(weekKey)}
              margin={0}
              padding={0}
              width="100%"
            >
              {week.map((day, dayIndex) => (
                <DayCell
                  day={day}
                  dayData={dayData}
                  dayIndex={dayIndex}
                  isSelected={day === selectedDay}
                  key={String(day ?? `blank-${dayIndex}`)}
                  month={month}
                  year={year}
                />
              ))}
            </Box>
          );
        })}
      </Box>
    </Box>
  );
}
