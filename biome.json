{"$schema": "https://biomejs.dev/schemas/2.1.2/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["**", "!node_modules", "!dist"]}, "formatter": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}}}, "javascript": {"formatter": {"quoteStyle": "single", "semicolons": "always"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on", "recommended": true}}}, "extends": ["ultracite"]}